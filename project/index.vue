<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="主键" prop="id">
            <el-input v-model="form.id" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="关联的项目唯一编码" prop="projectCode">
            <el-input v-model="form.projectCode" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="可提现范围(元) (格式 如 1000-50000)">
            <el-input v-model="form.drawableAmountRange" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="单笔提现步长(元)">
            <el-input v-model="form.drawableAmountStep" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="授信黑暗期 (格式 HH:mm-HH:mm)">
            <el-input v-model="form.creditDarkHours" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="用信黑暗期 (格式 HH:mm-HH:mm)">
            <el-input v-model="form.loanDarkHours" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="还款黑暗期 (格式 HH:mm-HH:mm)">
            <el-input v-model="form.repayDarkHours" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="资金方授信黑暗期">
            <el-input v-model="form.fundingCreditDarkHours" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="资金方用信黑暗期">
            <el-input v-model="form.fundingLoanDarkHours" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="资金方还款黑暗期">
            <el-input v-model="form.fundingRepayDarkHours" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="日授信限额(万元)">
            <el-input v-model="form.dailyCreditLimit" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="日放款限额(万元)">
            <el-input v-model="form.dailyLoanLimit" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="授信锁定期限(天)">
            <el-input v-model="form.creditLockDays" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="用信锁定期限(天)">
            <el-input v-model="form.loanLockDays" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="对客利率(%)">
            <el-input v-model="form.customerInterestRate" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="对资利率(%)">
            <el-input v-model="form.fundingInterestRate" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="年龄范围(岁) (格式 如 22-55 左右包含)">
            <el-input v-model="form.ageRange" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="支持的还款类型 (英文逗号分隔)">
            <el-input v-model="form.supportedRepayTypes" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="借款期限 (英文逗号分隔)">
            <el-input v-model="form.loanTerms" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="资方路由">
            <el-input v-model="form.capitalRoute" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="备注">
            <el-input v-model="form.remark" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="乐观锁">
            <el-input v-model="form.revision" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="创建人">
            <el-input v-model="form.createdBy" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="创建时间">
            <el-input v-model="form.createdTime" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="更新人">
            <el-input v-model="form.updatedBy" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="更新时间">
            <el-input v-model="form.updatedTime" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="项目时效类型（LONGTIME, TEMPORARY）">
            <el-input v-model="form.projectDurationType" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="临时配置有效期起" prop="tempStartTime">
            <el-input v-model="form.tempStartTime" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="临时配置有效期止" prop="tempEndTime">
            <el-input v-model="form.tempEndTime" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="启用状态" prop="enabled">
            <el-input v-model="form.enabled" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="是否年结">
            <el-input v-model="form.graceNext" style="width: 370px;" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="主键" />
        <el-table-column prop="projectCode" label="关联的项目唯一编码" />
        <el-table-column prop="drawableAmountRange" label="可提现范围(元) (格式 如 1000-50000)" />
        <el-table-column prop="drawableAmountStep" label="单笔提现步长(元)" />
        <el-table-column prop="creditDarkHours" label="授信黑暗期 (格式 HH:mm-HH:mm)" />
        <el-table-column prop="loanDarkHours" label="用信黑暗期 (格式 HH:mm-HH:mm)" />
        <el-table-column prop="repayDarkHours" label="还款黑暗期 (格式 HH:mm-HH:mm)" />
        <el-table-column prop="fundingCreditDarkHours" label="资金方授信黑暗期" />
        <el-table-column prop="fundingLoanDarkHours" label="资金方用信黑暗期" />
        <el-table-column prop="fundingRepayDarkHours" label="资金方还款黑暗期" />
        <el-table-column prop="dailyCreditLimit" label="日授信限额(万元)" />
        <el-table-column prop="dailyLoanLimit" label="日放款限额(万元)" />
        <el-table-column prop="creditLockDays" label="授信锁定期限(天)" />
        <el-table-column prop="loanLockDays" label="用信锁定期限(天)" />
        <el-table-column prop="customerInterestRate" label="对客利率(%)" />
        <el-table-column prop="fundingInterestRate" label="对资利率(%)" />
        <el-table-column prop="ageRange" label="年龄范围(岁) (格式 如 22-55 左右包含)" />
        <el-table-column prop="supportedRepayTypes" label="支持的还款类型 (英文逗号分隔)" />
        <el-table-column prop="loanTerms" label="借款期限 (英文逗号分隔)" />
        <el-table-column prop="capitalRoute" label="资方路由" />
        <el-table-column prop="remark" label="备注" />
        <el-table-column prop="revision" label="乐观锁" />
        <el-table-column prop="createdBy" label="创建人" />
        <el-table-column prop="createdTime" label="创建时间" />
        <el-table-column prop="updatedBy" label="更新人" />
        <el-table-column prop="updatedTime" label="更新时间" />
        <el-table-column prop="projectDurationType" label="项目时效类型（LONGTIME, TEMPORARY）" />
        <el-table-column prop="tempStartTime" label="临时配置有效期起" />
        <el-table-column prop="tempEndTime" label="临时配置有效期止" />
        <el-table-column prop="enabled" label="启用状态" />
        <el-table-column prop="graceNext" label="是否年结" />
        <el-table-column v-if="checkPer(['admin','projectElements:edit','projectElements:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudProjectElements from '@/api/projectElements'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = { id: null, projectCode: null, drawableAmountRange: null, drawableAmountStep: null, creditDarkHours: null, loanDarkHours: null, repayDarkHours: null, fundingCreditDarkHours: null, fundingLoanDarkHours: null, fundingRepayDarkHours: null, dailyCreditLimit: null, dailyLoanLimit: null, creditLockDays: null, loanLockDays: null, customerInterestRate: null, fundingInterestRate: null, ageRange: null, supportedRepayTypes: null, loanTerms: null, capitalRoute: null, remark: null, revision: null, createdBy: null, createdTime: null, updatedBy: null, updatedTime: null, projectDurationType: null, tempStartTime: null, tempEndTime: null, enabled: null, graceNext: null }
export default {
  name: 'ProjectElements',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  cruds() {
    return CRUD({ title: 'project', url: 'api/projectElements', idField: 'id', sort: 'id,desc', crudMethod: { ...crudProjectElements }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'projectElements:add'],
        edit: ['admin', 'projectElements:edit'],
        del: ['admin', 'projectElements:del']
      },
      rules: {
        id: [
          { required: true, message: '主键不能为空', trigger: 'blur' }
        ],
        projectCode: [
          { required: true, message: '关联的项目唯一编码不能为空', trigger: 'blur' }
        ],
        tempStartTime: [
          { required: true, message: '临时配置有效期起不能为空', trigger: 'blur' }
        ],
        tempEndTime: [
          { required: true, message: '临时配置有效期止不能为空', trigger: 'blur' }
        ],
        enabled: [
          { required: true, message: '启用状态不能为空', trigger: 'blur' }
        ]
      }    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    }
  }
}
</script>

<style scoped>

</style>
