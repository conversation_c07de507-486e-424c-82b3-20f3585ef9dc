/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.jinghang.cash.modules.project.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import java.sql.Timestamp;
import java.math.BigDecimal;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
* @description /
* <AUTHOR>
* @date 2025-08-26
**/
@Data
@TableName("project_elements")
public class ProjectElements implements Serializable {

    @TableId(value = "id")
    @ApiModelProperty(value = "主键")
    private String id;

    @NotBlank
    @ApiModelProperty(value = "关联的项目唯一编码")
    private String projectCode;

    @ApiModelProperty(value = "可提现范围(元) (格式 如 1000-50000)")
    private String drawableAmountRange;

    @ApiModelProperty(value = "单笔提现步长(元)")
    private String drawableAmountStep;

    @ApiModelProperty(value = "授信黑暗期 (格式 HH:mm-HH:mm)")
    private String creditDarkHours;

    @ApiModelProperty(value = "用信黑暗期 (格式 HH:mm-HH:mm)")
    private String loanDarkHours;

    @ApiModelProperty(value = "还款黑暗期 (格式 HH:mm-HH:mm)")
    private String repayDarkHours;

    @ApiModelProperty(value = "资金方授信黑暗期")
    private String fundingCreditDarkHours;

    @ApiModelProperty(value = "资金方用信黑暗期")
    private String fundingLoanDarkHours;

    @ApiModelProperty(value = "资金方还款黑暗期")
    private String fundingRepayDarkHours;

    @ApiModelProperty(value = "日授信限额(万元)")
    private BigDecimal dailyCreditLimit;

    @ApiModelProperty(value = "日放款限额(万元)")
    private BigDecimal dailyLoanLimit;

    @ApiModelProperty(value = "授信锁定期限(天)")
    private Integer creditLockDays;

    @ApiModelProperty(value = "用信锁定期限(天)")
    private Integer loanLockDays;

    @ApiModelProperty(value = "对客利率(%)")
    private String customerInterestRate;

    @ApiModelProperty(value = "对资利率(%)")
    private String fundingInterestRate;

    @ApiModelProperty(value = "年龄范围(岁) (格式 如 22-55 左右包含)")
    private String ageRange;

    @ApiModelProperty(value = "支持的还款类型 (英文逗号分隔)")
    private String supportedRepayTypes;

    @ApiModelProperty(value = "借款期限 (英文逗号分隔)")
    private String loanTerms;

    @ApiModelProperty(value = "资方路由")
    private String capitalRoute;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "乐观锁")
    private String revision;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private Timestamp createdTime;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "更新时间")
    private Timestamp updatedTime;

    @ApiModelProperty(value = "项目时效类型（LONGTIME, TEMPORARY）")
    private String projectDurationType;

    @NotNull
    @ApiModelProperty(value = "临时配置有效期起")
    private Timestamp tempStartTime;

    @NotNull
    @ApiModelProperty(value = "临时配置有效期止")
    private Timestamp tempEndTime;

    @NotBlank
    @ApiModelProperty(value = "启用状态")
    private String enabled;

    @ApiModelProperty(value = "是否年结")
    private String graceNext;

    public void copy(ProjectElements source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
