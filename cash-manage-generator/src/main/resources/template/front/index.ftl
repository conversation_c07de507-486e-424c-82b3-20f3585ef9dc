<#--noinspection ALL-->
<template>
  <div class="app-container">
    <!--搜索表单-->
    <#if hasQuery>
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <#if queryColumns??>
        <#list queryColumns as column>
          <#if column.queryType != 'BetWeen'>
      <el-form-item label="<#if column.remark != ''>${column.remark}<#else>${column.changeColumnName}</#if>">
        <el-input v-model="queryParams.${column.changeColumnName}" clearable placeholder="<#if column.remark != ''>${column.remark}<#else>${column.changeColumnName}</#if>" style="width: 185px;" @keyup.enter.native="handleQuery" />
      </el-form-item>
          </#if>
        </#list>
      </#if>
      <#if betweens??>
        <#list betweens as column>
          <#if column.queryType = 'BetWeen'>
      <el-form-item label="${column.changeColumnName}">
        <el-date-picker
          v-model="queryParams.${column.changeColumnName}"
          type="daterange"
          start-placeholder="${column.changeColumnName}开始"
          end-placeholder="${column.changeColumnName}结束"
          class="date-item"
        />
      </el-form-item>
          </#if>
        </#list>
      </#if>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    </#if>

    <!--操作按钮-->
    <div class="head-container">
      <el-button v-permission="['admin','${changeClassName}:add']" type="primary" @click="handleAdd">新增</el-button>
      <el-button v-permission="['admin','${changeClassName}:del']" type="danger" :disabled="multiple" @click="handleDelete">删除</el-button>
    </div>

    <!--表单对话框-->
    <el-dialog :close-on-click-modal="false" :before-close="handleCancel" :visible.sync="open" :title="title" width="500px">
      <el-form ref="form" :model="form" <#if isNotNullColumns??>:rules="rules"</#if> size="small" label-width="80px">
    <#if columns??>
      <#list columns as column>
        <#if column.formShow>
        <el-form-item label="<#if column.remark != ''>${column.remark}<#else>${column.changeColumnName}</#if>"<#if column.istNotNull> prop="${column.changeColumnName}"</#if>>
          <#if column.formType = 'Input'>
          <el-input v-model="form.${column.changeColumnName}" style="width: 370px;" />
          <#elseif column.formType = 'Textarea'>
          <el-input v-model="form.${column.changeColumnName}" :rows="3" type="textarea" style="width: 370px;" />
          <#elseif column.formType = 'Radio'>
            <#if (column.dictName)?? && (column.dictName)!="">
          <el-radio v-model="form.${column.changeColumnName}" v-for="item in dict.${column.dictName}" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
            <#else>
              未设置字典，请手动设置 Radio
            </#if>
          <#elseif column.formType = 'Select'>
            <#if (column.dictName)?? && (column.dictName)!="">
          <el-select v-model="form.${column.changeColumnName}" filterable placeholder="请选择">
            <el-option
              v-for="item in dict.${column.dictName}"
              :key="item.id"
              :label="item.label"
              :value="item.value" />
          </el-select>
            <#else>
          未设置字典，请手动设置 Select
            </#if>
          <#else>
          <el-date-picker v-model="form.${column.changeColumnName}" type="datetime" style="width: 370px;" />
          </#if>
        </el-form-item>
        </#if>
      </#list>
    </#if>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="handleCancel">取消</el-button>
        <el-button :loading="loading" type="primary" @click="submitForm">确认</el-button>
      </div>
    </el-dialog>
    <!--表格渲染-->
    <el-table ref="table" v-loading="loading" :data="list" size="small" style="width: 100%;" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      <#if columns??>
        <#list columns as column>
          <#if column.columnShow>
            <#if (column.dictName)?? && (column.dictName)!="">
      <el-table-column prop="${column.changeColumnName}" label="<#if column.remark != ''>${column.remark}<#else>${column.changeColumnName}</#if>">
        <template slot-scope="scope">
          {{ dict.label.${column.dictName}[scope.row.${column.changeColumnName}] }}
        </template>
      </el-table-column>
            <#else>
      <el-table-column prop="${column.changeColumnName}" label="<#if column.remark != ''>${column.remark}<#else>${column.changeColumnName}</#if>" />
            </#if>
          </#if>
        </#list>
      </#if>
      <el-table-column v-permission="['admin','${changeClassName}:edit','${changeClassName}:del']" label="操作" width="150px" align="center">
        <template slot-scope="scope">
          <el-button v-permission="['admin','${changeClassName}:edit']" type="text" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button v-permission="['admin','${changeClassName}:del']" type="text" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--分页组件-->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
  </div>
</template>

<script>
import { list${className}, get${className}, del${className}, add${className}, update${className} } from '@/api/${changeClassName}'
import Pagination from '@/components/Pagination'
<#if hasDict>
import { get as getDictByName } from '@/api/system/dictDetail'
</#if>

const defaultForm = { <#if columns??><#list columns as column>${column.changeColumnName}: null<#if column_has_next>, </#if></#list></#if> }
export default {
  name: '${className}',
  components: { Pagination },
  <#if hasDict>
  dicts: [<#if hasDict??><#list dicts as dict>'${dict}'<#if dict_has_next>, </#if></#list></#if>],
  </#if>
  data() {
    return {
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10<#if queryColumns??><#list queryColumns as column><#if column.queryType != 'BetWeen'>,
        ${column.changeColumnName}: undefined</#if></#list></#if><#if betweens??><#list betweens as column><#if column.queryType = 'BetWeen'>,
        ${column.changeColumnName}: undefined</#if></#list></#if>
      },
      // 表单参数
      form: { ...defaultForm },
      // 表单校验
      rules: {
        <#if isNotNullColumns??>
        <#list isNotNullColumns as column>
        <#if column.istNotNull>
        ${column.changeColumnName}: [
          { required: true, message: '<#if column.remark != ''>${column.remark}</#if>不能为空', trigger: 'blur' }
        ]<#if column_has_next>,</#if>
        </#if>
        </#list>
        </#if>
      },
      // 列表数据
      list: [],
      // 总条数
      total: 0,
      // 是否显示弹出层
      open: false,
      // 弹出层标题
      title: '',
      // 是否显示loading
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true<#if hasDict>,
      // 字典数据
      <#list dicts as dict>
      ${dict}Options: []<#if dict_has_next>,</#if>
      </#list></#if>
    }
  },
  created() {
    this.getList()
    <#if hasDict>
    this.getDicts()
    </#if>
  },
  methods: {
    /** 查询${apiAlias}列表 */
    getList() {
      this.loading = true
      list${className}(this.queryParams).then(response => {
        this.list = response.data.list || response.data.content || response.data
        this.total = response.data.total || response.data.totalElements || this.list.length
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    <#if hasDict>
    /** 获取字典数据 */
    getDicts() {
      <#list dicts as dict>
      getDictByName('${dict}').then(response => {
        this.${dict}Options = response.content || response.data || []
      })
      </#list>
    },
    </#if>
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.$refs.queryForm.resetFields()
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.${pkChangeColName})
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加${apiAlias}'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const ${pkChangeColName} = row.${pkChangeColName} || this.ids[0]
      get${className}(${pkChangeColName}).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改${apiAlias}'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.loading = true
          if (this.form.${pkChangeColName} != null) {
            update${className}(this.form).then(response => {
              this.$message.success('修改成功')
              this.open = false
              this.getList()
            }).finally(() => {
              this.loading = false
            })
          } else {
            add${className}(this.form).then(response => {
              this.$message.success('新增成功')
              this.open = false
              this.getList()
            }).finally(() => {
              this.loading = false
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ${pkChangeColName}s = row.${pkChangeColName} ? [row.${pkChangeColName}] : this.ids
      this.$confirm('是否确认删除${apiAlias}编号为"' + ${pkChangeColName}s + '"的数据项？').then(() => {
        return del${className}(${pkChangeColName}s)
      }).then(() => {
        this.getList()
        this.$message.success('删除成功')
      })
    },
    /** 取消按钮 */
    handleCancel() {
      this.open = false
      this.reset()
    },
    /** 表单重置 */
    reset() {
      this.form = { ...defaultForm }
      this.$refs['form'] && this.$refs['form'].resetFields()
    }
  }
}
</script>

<style scoped>

</style>
