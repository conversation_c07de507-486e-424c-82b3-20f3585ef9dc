<template>
  <div class="app-container">
    <!--搜索表单-->

    <!--操作按钮-->
    <div class="head-container">
      <el-button v-permission="['admin','genTest:add']" type="primary" @click="handleAdd">新增</el-button>
      <el-button v-permission="['admin','genTest:del']" type="danger" :disabled="multiple" @click="handleDelete">删除</el-button>
    </div>

    <!--表单对话框-->
    <el-dialog :close-on-click-modal="false" :before-close="handleCancel" :visible.sync="open" :title="title" width="500px">
      <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
        <el-form-item label="主键" prop="id">
          <el-input v-model="form.id" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="form.remark" style="width: 370px;" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="handleCancel">取消</el-button>
        <el-button :loading="loading" type="primary" @click="submitForm">确认</el-button>
      </div>
    </el-dialog>
    <!--表格渲染-->
    <el-table ref="table" v-loading="loading" :data="list" size="small" style="width: 100%;" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      <el-table-column prop="id" label="主键" />
      <el-table-column prop="remark" label="描述" />
      <el-table-column v-permission="['admin','genTest:edit','genTest:del']" label="操作" width="150px" align="center">
        <template slot-scope="scope">
          <el-button v-permission="['admin','genTest:edit']" type="text" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button v-permission="['admin','genTest:del']" type="text" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--分页组件-->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
  </div>
</template>

<script>
import { listGenTest, getGenTest, delGenTest, addGenTest, updateGenTest } from '@/api/genTest'
import Pagination from '@/components/Pagination'

const defaultForm = { id: null, remark: null }
export default {
  name: 'GenTest',
  components: { Pagination },
  data() {
    return {
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      // 表单参数
      form: { ...defaultForm },
      // 表单校验
      rules: {
        id: [
          { required: true, message: '主键不能为空', trigger: 'blur' }
        ]
      },
      // 列表数据
      list: [],
      // 总条数
      total: 0,
      // 是否显示弹出层
      open: false,
      // 弹出层标题
      title: '',
      // 是否显示loading
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询com.jinghang.cash.modules.project列表 */
    getList() {
      this.loading = true
      listGenTest(this.queryParams).then(response => {
        this.list = response.data.list || response.data.content || response.data
        this.total = response.data.total || response.data.totalElements || this.list.length
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加com.jinghang.cash.modules.project'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids[0]
      getGenTest(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改com.jinghang.cash.modules.project'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.loading = true
          if (this.form.id != null) {
            updateGenTest(this.form).then(response => {
              this.$message.success('修改成功')
              this.open = false
              this.getList()
            }).finally(() => {
              this.loading = false
            })
          } else {
            addGenTest(this.form).then(response => {
              this.$message.success('新增成功')
              this.open = false
              this.getList()
            }).finally(() => {
              this.loading = false
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id ? [row.id] : this.ids
      this.$confirm('是否确认删除com.jinghang.cash.modules.project编号为"' + ids + '"的数据项？').then(() => {
        return delGenTest(ids)
      }).then(() => {
        this.getList()
        this.$message.success('删除成功')
      })
    },
    /** 取消按钮 */
    handleCancel() {
      this.open = false
      this.reset()
    },
    /** 表单重置 */
    reset() {
      this.form = { ...defaultForm }
      this.$refs['form'] && this.$refs['form'].resetFields()
    }
  }
}
</script>

<style scoped>

</style>
