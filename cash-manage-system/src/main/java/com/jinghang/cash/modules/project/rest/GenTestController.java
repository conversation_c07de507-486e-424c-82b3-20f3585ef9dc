/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.jinghang.cash.modules.project.rest;

import com.jinghang.cash.modules.manage.RestResult;
import com.jinghang.cash.modules.project.domain.GenTest;
import com.jinghang.cash.modules.project.service.GenTestService;
import com.jinghang.cash.modules.project.domain.dto.GenTestQueryCriteria;
import com.jinghang.cash.utils.PageResult;
import lombok.RequiredArgsConstructor;
import java.util.List;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

/**
* <AUTHOR>
* @date 2025-08-26
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "com.jinghang.cash.modules.project")
@RequestMapping("/api/genTest")
public class GenTestController {

    private final GenTestService genTestService;

    @GetMapping("/queryPage")
    @ApiOperation("查询com.jinghang.cash.modules.project")
    @PreAuthorize("@el.check('genTest:list')")
    public RestResult<PageResult<GenTest>> queryGenTest(GenTestQueryCriteria criteria){
        return RestResult.success(genTestService.queryAllPage(criteria));
    }

    @PostMapping("/create")
    @ApiOperation("新增com.jinghang.cash.modules.project")
    @PreAuthorize("@el.check('genTest:add')")
    public RestResult<Object> createGenTest(@Validated @RequestBody GenTest resources){
        genTestService.create(resources);
        return RestResult.success(HttpStatus.CREATED);
    }

    @PutMapping("/update")
    @ApiOperation("修改com.jinghang.cash.modules.project")
    @PreAuthorize("@el.check('genTest:edit')")
    public RestResult<Object> updateGenTest(@Validated @RequestBody GenTest resources){
        genTestService.update(resources);
        return RestResult.success(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping("/del")
    @ApiOperation("删除com.jinghang.cash.modules.project")
    @PreAuthorize("@el.check('genTest:del')")
    public RestResult<Object> deleteGenTest(@ApiParam(value = "传ID数组[]") @RequestBody List<Integer> ids) {
        genTestService.deleteAll(ids);
        return RestResult.success(HttpStatus.OK);
    }

    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('genTest:list')")
    public void exportGenTest(HttpServletResponse response, GenTestQueryCriteria criteria) throws IOException {
    genTestService.download(genTestService.queryAll(criteria), response);
    }
}